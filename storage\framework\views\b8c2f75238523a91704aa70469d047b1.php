

<?php $__env->startSection('title', 'Ngambiskuy - Kuasai Skill Tech, Raih Karir Impian'); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<section class="bg-gradient-to-br from-orange-50 via-white to-orange-100 py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid lg:grid-cols-2 gap-12 items-center">
            <!-- Left Content -->
            <div class="space-y-8">
                <div class="space-y-4">
                    <div class="inline-flex items-center px-3 py-1 rounded-full bg-primary/10 text-primary text-sm font-medium">
                        🚀 Platform Pembelajaran Bertenaga AI
                    </div>
                    <h1 class="text-4xl lg:text-6xl font-bold text-gray-900 leading-tight">
                        Kuasai Skill Tech,
                        <span class="text-transparent bg-clip-text bg-gradient-to-r from-primary to-red-500">
                            <PERSON><PERSON>
                        </span>
                    </h1>
                    <p class="text-xl text-gray-600 leading-relaxed">
                        Bergabunglah dengan ribuan mahasiswa dan profesional Indonesia yang menguasai skill teknologi terdepan
                        dengan platform pembelajaran AI yang dipersonalisasi.
                    </p>
                </div>

                <!-- Stats -->
                

                <!-- CTA Buttons -->
                <div class="flex flex-col sm:flex-row gap-4">
                    <a href="<?php echo e(route('courses.index')); ?>" class="btn btn-primary btn-lg">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                        Mulai Belajar
                    </a>
                    <a href="<?php echo e(route('tutor.register.terms')); ?>" class="btn btn-outline btn-lg">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                        Jadi Pengajar
                    </a>
                    
                </div>
            </div>

            <!-- Right Content - Hero Image -->
            <div class="relative">
                <div class="relative bg-gradient-to-br from-primary/10 to-orange-200 rounded-2xl p-8 shadow-2xl">
                    <img src="<?php echo e(asset('images/hero-students.svg')); ?>" alt="Siswa belajar skill teknologi" class="w-full h-auto rounded-lg">

                    <!-- Floating Cards -->
                    <div class="absolute -top-4 -left-4 bg-white rounded-lg shadow-lg p-4 border">
                        <div class="flex items-center space-x-2">
                            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                <span class="text-green-600 text-sm">✓</span>
                            </div>
                            <div>
                                <p class="text-sm font-medium">Kursus Selesai</p>
                                <p class="text-xs text-gray-500">Dasar-dasar React.js</p>
                            </div>
                        </div>
                    </div>

                    <div class="absolute -bottom-4 -right-4 bg-white rounded-lg shadow-lg p-4 border">
                        <div class="flex items-center space-x-2">
                            <div class="w-8 h-8 bg-primary/20 rounded-full flex items-center justify-center">
                                <span class="text-primary text-sm">🎯</span>
                            </div>
                            <div>
                                <p class="text-sm font-medium">Kecocokan Karir</p>
                                <p class="text-xs text-gray-500">85% Frontend Developer</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Course Catalog Section -->
<section class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-12">
            <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">Jelajahi Kursus</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Mulai perjalanan belajar Anda dengan kursus gratis atau tingkatkan skill dengan kursus premium
            </p>
        </div>

        <!-- Tabs -->
        <div class="flex justify-center mb-8">
            <div class="flex bg-white rounded-lg p-1 shadow-sm border">
                <button class="course-tab active flex items-center space-x-2 px-6 py-3 rounded-md font-medium" data-tab="gratis">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                    <span>Gratis</span>
                </button>
                <button class="course-tab flex items-center space-x-2 px-6 py-3 rounded-md font-medium" data-tab="premium">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path>
                    </svg>
                    <span>Premium</span>
                </button>
                <button class="course-tab flex items-center space-x-2 px-6 py-3 rounded-md font-medium" data-tab="tutorial">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.586a1 1 0 01.707.293l2.414 2.414a1 1 0 00.707.293H15M9 10v4a2 2 0 002 2h2a2 2 0 002-2v-4M9 10V9a2 2 0 012-2h2a2 2 0 012 2v1"></path>
                    </svg>
                    <span>Tutorial</span>
                </button>
                <button class="course-tab flex items-center space-x-2 px-6 py-3 rounded-md font-medium" data-tab="webinar">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                    <span>Webinar</span>
                </button>
            </div>
        </div>

        <!-- Search and Filters -->
        <div class="mb-8">
            <div class="flex flex-col lg:flex-row gap-4">
                <!-- Search -->
                <div class="relative flex-1">
                    <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    <input type="text" placeholder="Cari kursus..." class="input pl-10 w-full" id="course-search">
                </div>

                <!-- Filters -->
                <div class="flex gap-4">
                    <select class="select w-48" id="category-filter">
                        <option value="all">Semua Kategori</option>
                        <option value="Programming">Programming</option>
                        <option value="Web Development">Web Development</option>
                        <option value="Data Science">Data Science</option>
                        <option value="Mobile Development">Mobile Development</option>
                        <option value="Technology">Technology</option>
                    </select>

                    <select class="select w-40" id="level-filter">
                        <option value="all">Semua Level</option>
                        <option value="Pemula">Pemula</option>
                        <option value="Menengah">Menengah</option>
                        <option value="Lanjutan">Lanjutan</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Course Grid -->
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6" id="course-grid">
            <?php $__currentLoopData = $courses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="course-card bg-white rounded-lg shadow-sm hover:shadow-lg transition-shadow duration-300"
                 data-type="<?php echo e($course['type']); ?>"
                 data-category="<?php echo e($course['category']); ?>"
                 data-level="<?php echo e($course['level']); ?>">
                <div class="relative">
                    <img src="<?php echo e(asset('images/' . $course['image'])); ?>" alt="<?php echo e($course['title']); ?>" class="w-full h-48 object-cover rounded-t-lg">

                    <?php if($course['type'] === 'webinar' && isset($course['is_live']) && $course['is_live']): ?>
                        <span class="absolute top-3 left-3 bg-red-600 text-white text-xs px-2 py-1 rounded animate-pulse">🔴 LIVE</span>
                    <?php endif; ?>

                    <?php if($course['price'] === 'Free'): ?>
                        <span class="absolute top-3 right-3 bg-green-600 text-white text-xs px-2 py-1 rounded">GRATIS</span>
                    <?php elseif(isset($course['original_price'])): ?>
                        <span class="absolute top-3 right-3 bg-red-600 text-white text-xs px-2 py-1 rounded">DISKON</span>
                    <?php endif; ?>
                </div>

                <div class="p-6">
                    <div class="space-y-3">
                        <span class="inline-block bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded"><?php echo e($course['category']); ?></span>

                        <h3 class="text-lg font-semibold line-clamp-2"><?php echo e($course['title']); ?></h3>

                        <p class="text-gray-600 text-sm line-clamp-2"><?php echo e($course['description']); ?></p>

                        <div class="flex items-center justify-between text-sm text-gray-500">
                            <div class="flex items-center space-x-1">
                                <svg class="w-4 h-4 text-yellow-500" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path>
                                </svg>
                                <span><?php echo e($course['rating']); ?></span>
                            </div>
                            <div class="flex items-center space-x-1">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                </svg>
                                <span><?php echo e(number_format($course['students'])); ?></span>
                            </div>
                            <div class="flex items-center space-x-1">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span><?php echo e($course['duration']); ?></span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="px-6 pb-6">
                    <div class="flex items-center justify-between">
                        <div class="flex flex-col">
                            <?php if($course['price'] === 'Free'): ?>
                                <span class="text-xl font-bold text-green-600">Free</span>
                            <?php else: ?>
                                <span class="text-xl font-bold text-primary"><?php echo e($course['price']); ?></span>
                                <?php if(isset($course['original_price'])): ?>
                                    <span class="text-sm text-gray-500 line-through"><?php echo e($course['original_price']); ?></span>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                        <a href="<?php echo e(route('course.show', $course['slug'])); ?>" class="btn btn-primary">Mulai Belajar</a>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <!-- Load More -->
        <div class="text-center mt-12">
            <a href="<?php echo e(route('courses.index')); ?>" class="btn btn-outline btn-lg">Muat Lebih Banyak Kursus</a>
        </div>
    </div>
</section>

<!-- Exam System Section -->
<section class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-16">
            <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">Sistem Ujian Ngambiskuy</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
                Ikut ujian untuk menguji kemampuan atau buat ujian sendiri untuk tim dan siswa Anda
            </p>

            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="<?php echo e(route('exams.index')); ?>" class="btn btn-primary btn-lg">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Ikut Ujian
                </a>
                <a href="<?php echo e(route('tutor.register.terms')); ?>" class="btn btn-outline btn-lg">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Buat Ujian Sendiri
                </a>
            </div>
        </div>

        <!-- Feature Cards -->
        <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
            <!-- Buat Ujian Sendiri -->
            <div class="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Buat Ujian Sendiri</h3>
                <p class="text-gray-600 text-sm">Buat ujian custom dengan berbagai jenis soal dan atur waktu sesuai kebutuhan</p>
            </div>

            <!-- Ujian Berkelompok -->
            <div class="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Ujian Berkelompok</h3>
                <p class="text-gray-600 text-sm">Adakan ujian untuk tim atau kelas dengan monitoring real-time</p>
            </div>

            <!-- Analisis Hasil -->
            <div class="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Analisis Hasil</h3>
                <p class="text-gray-600 text-sm">Dapatkan laporan detail dan analisis performa peserta ujian</p>
            </div>

            <!-- Sertifikat Digital -->
            <div class="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow">
                <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Sertifikat Digital</h3>
                <p class="text-gray-600 text-sm">Berikan sertifikat digital otomatis untuk peserta yang lulus</p>
            </div>
        </div>

        <!-- Available Exams Section -->
        <div class="mb-16">
            <div class="flex justify-between items-center mb-8">
                <h3 class="text-2xl font-bold text-gray-900">Ujian Tersedia</h3>
                <a href="<?php echo e(route('exams.index')); ?>" class="btn btn-outline">Lihat Semua Ujian</a>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                <?php $__empty_1 = true; $__currentLoopData = $featuredExams; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $exam): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <div class="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow">
                    <div class="flex justify-between items-start mb-4">
                        <?php if($exam['price'] === 'GRATIS'): ?>
                            <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full font-medium">Gratis</span>
                        <?php else: ?>
                            <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full font-medium">Premium</span>
                        <?php endif; ?>
                        <span class="text-gray-500 text-sm"><?php echo e($exam['total_questions']); ?> soal</span>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2"><?php echo e($exam['title']); ?></h4>
                    <p class="text-gray-600 text-sm mb-4"><?php echo e($exam['category']); ?></p>

                    <div class="flex items-center gap-4 text-sm text-gray-500 mb-4">
                        <div class="flex items-center gap-1">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span><?php echo e($exam['time_limit']); ?> menit</span>
                        </div>
                        <div class="flex items-center gap-1">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                            <span><?php echo e($exam['total_enrollments']); ?></span>
                        </div>
                        <div class="flex items-center gap-1">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                            <span><?php echo e($exam['difficulty_level']); ?></span>
                        </div>
                    </div>

                    <div class="flex justify-between items-center">
                        <span class="text-xl font-bold <?php echo e($exam['price'] === 'GRATIS' ? 'text-green-600' : 'text-gray-900'); ?>"><?php echo e($exam['price']); ?></span>
                        <a href="<?php echo e(route('exams.show', $exam['id'])); ?>" class="btn btn-primary">
                            <?php echo e($exam['price'] === 'GRATIS' ? 'Mulai Gratis' : 'Daftar Ujian'); ?>

                        </a>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <!-- Fallback if no exams available -->
                <div class="col-span-full text-center py-12">
                    <p class="text-gray-500">Belum ada ujian tersedia saat ini.</p>
                    <a href="<?php echo e(route('tutor.register.terms')); ?>" class="btn btn-primary mt-4">Buat Ujian Pertama</a>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Stats -->
        <div class="bg-white rounded-2xl p-8 shadow-lg">
            <div class="grid grid-cols-2 lg:grid-cols-4 gap-8 text-center">
                <div>
                    <div class="text-3xl font-bold text-gray-900 mb-2"><?php echo e(number_format($examStats['total_exams_taken'])); ?>+</div>
                    <div class="text-gray-600">Ujian Diikuti</div>
                </div>
                <div>
                    <div class="text-3xl font-bold text-blue-600 mb-2"><?php echo e(number_format($examStats['available_exams'])); ?>+</div>
                    <div class="text-gray-600">Ujian Tersedia</div>
                </div>
                <div>
                    <div class="text-3xl font-bold text-green-600 mb-2"><?php echo e(number_format($examStats['certificates_issued'])); ?>+</div>
                    <div class="text-gray-600">Sertifikat Diterbitkan</div>
                </div>
                <div>
                    <div class="text-3xl font-bold text-purple-600 mb-2"><?php echo e($examStats['satisfaction_rate']); ?>%</div>
                    <div class="text-gray-600">Tingkat Kepuasan</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Blog Section -->
<section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-16">
            <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">Blog Ngambiskuy</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Tetap update dengan tren tech terbaru, tips karir, dan kisah sukses dari komunitas tech Indonesia
            </p>
        </div>

        <!-- Featured Post -->
        <?php $featuredPost = $blogPosts->firstWhere('is_featured', true); ?>
        <?php if($featuredPost): ?>
        <div class="bg-white rounded-lg shadow-sm hover:shadow-lg transition-shadow mb-12 overflow-hidden">
            <div class="grid md:grid-cols-2 gap-0">
                <div class="relative">
                    <img src="<?php echo e($featuredPost->featured_image ? asset('storage/' . $featuredPost->featured_image) : asset('images/blog/placeholder.svg')); ?>"
                         alt="<?php echo e($featuredPost->title); ?>" class="w-full h-64 md:h-full object-cover">
                    <span class="absolute top-4 left-4 bg-primary text-white text-xs px-2 py-1 rounded">Featured</span>
                </div>
                <div class="p-8 flex flex-col justify-center">
                    <?php if($featuredPost->category): ?>
                    <span class="inline-block bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded w-fit mb-3"><?php echo e($featuredPost->category->name); ?></span>
                    <?php endif; ?>
                    <h3 class="text-2xl font-bold text-gray-900 mb-4"><?php echo e($featuredPost->title); ?></h3>
                    <p class="text-gray-600 mb-6"><?php echo e($featuredPost->excerpt); ?></p>

                    <div class="flex items-center space-x-3 mb-6">
                        <img src="<?php echo e($featuredPost->author->profile_picture ? asset('storage/' . $featuredPost->author->profile_picture) : asset('images/avatars/placeholder.svg')); ?>"
                             alt="<?php echo e($featuredPost->author->name); ?>" class="w-8 h-8 rounded-full">
                        <div>
                            <p class="text-sm font-medium"><?php echo e($featuredPost->author->name); ?></p>
                            <div class="flex items-center space-x-2 text-xs text-gray-500">
                                <span><?php echo e($featuredPost->formatted_published_date); ?></span>
                                <span>•</span>
                                <span><?php echo e($featuredPost->read_time_text); ?></span>
                            </div>
                        </div>
                    </div>

                    <a href="<?php echo e(route('blog.show', $featuredPost->slug)); ?>" class="btn btn-primary w-fit">Baca Selengkapnya</a>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Regular Posts Grid -->
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            <?php $__currentLoopData = $blogPosts->where('is_featured', false); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="bg-white rounded-lg shadow-sm hover:shadow-lg transition-shadow duration-300 overflow-hidden">
                <div class="relative">
                    <img src="<?php echo e($post->featured_image ? asset('storage/' . $post->featured_image) : asset('images/blog/placeholder.svg')); ?>"
                         alt="<?php echo e($post->title); ?>" class="w-full h-48 object-cover">
                    <?php if($post->category): ?>
                    <span class="absolute top-3 left-3 bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded"><?php echo e($post->category->name); ?></span>
                    <?php endif; ?>
                </div>

                <div class="p-6">
                    <div class="space-y-4">
                        <h4 class="text-lg font-semibold line-clamp-2 hover:text-primary cursor-pointer transition-colors">
                            <a href="<?php echo e(route('blog.show', $post->slug)); ?>"><?php echo e($post->title); ?></a>
                        </h4>

                        <p class="text-gray-600 text-sm line-clamp-3"><?php echo e($post->excerpt); ?></p>

                        <div class="flex items-center space-x-3">
                            <img src="<?php echo e($post->author->profile_picture ? asset('storage/' . $post->author->profile_picture) : asset('images/avatars/placeholder.svg')); ?>"
                                 alt="<?php echo e($post->author->name); ?>" class="w-8 h-8 rounded-full">
                            <div class="flex-1">
                                <p class="text-sm font-medium"><?php echo e($post->author->name); ?></p>
                                <div class="flex items-center space-x-2 text-xs text-gray-500">
                                    <span><?php echo e($post->formatted_published_date); ?></span>
                                    <span>•</span>
                                    <span><?php echo e($post->read_time_text); ?></span>
                                </div>
                            </div>
                        </div>

                        <a href="<?php echo e(route('blog.show', $post->slug)); ?>" class="btn btn-ghost w-full text-primary hover:text-primary/80">Baca Artikel</a>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <!-- CTA -->
        <div class="text-center">
            <a href="<?php echo e(route('blog.index')); ?>" class="btn btn-outline btn-lg">Lihat Semua Artikel</a>
        </div>
    </div>
</section>

<!-- Testimonials Section -->
<section class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-16">
            <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">Kisah Sukses dari Siswa Kami</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Bergabunglah dengan ribuan siswa yang telah mengubah karir mereka dengan Ngambiskuy
            </p>
        </div>

        <!-- Testimonials Grid -->
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php $__currentLoopData = $testimonials; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $testimonial): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="bg-white rounded-lg shadow-sm hover:shadow-lg transition-shadow duration-300 p-6">
                <div class="space-y-4">
                    <!-- Quote Icon -->
                    <svg class="w-8 h-8 text-primary opacity-20" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z"/>
                    </svg>

                    <!-- Rating -->
                    <div class="flex items-center space-x-1">
                        <?php for($i = 0; $i < $testimonial['rating']; $i++): ?>
                        <svg class="w-4 h-4 text-yellow-500" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path>
                        </svg>
                        <?php endfor; ?>
                    </div>

                    <!-- Content -->
                    <p class="text-gray-700 leading-relaxed">"<?php echo e($testimonial['content']); ?>"</p>

                    <!-- Course -->
                    <div class="text-sm text-primary font-medium">Kursus: <?php echo e($testimonial['course']); ?></div>

                    <!-- Author -->
                    <div class="flex items-center space-x-3 pt-4 border-t">
                        <img src="<?php echo e(asset('images/' . $testimonial['avatar'])); ?>" alt="<?php echo e($testimonial['name']); ?>" class="w-10 h-10 rounded-full">
                        <div>
                            <p class="font-semibold text-gray-900"><?php echo e($testimonial['name']); ?></p>
                            <p class="text-sm text-gray-600"><?php echo e($testimonial['role']); ?></p>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <!-- Stats -->
        
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Course tab functionality
    const courseTabs = document.querySelectorAll('.course-tab');
    const courseCards = document.querySelectorAll('.course-card');

    courseTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const tabType = this.dataset.tab;

            // Update active tab
            courseTabs.forEach(t => t.classList.remove('active'));
            this.classList.add('active');

            // Filter courses
            filterCourses();
        });
    });

    // Search and filter functionality
    const searchInput = document.getElementById('course-search');
    const categoryFilter = document.getElementById('category-filter');
    const levelFilter = document.getElementById('level-filter');

    if (searchInput) {
        searchInput.addEventListener('input', filterCourses);
    }
    if (categoryFilter) {
        categoryFilter.addEventListener('change', filterCourses);
    }
    if (levelFilter) {
        levelFilter.addEventListener('change', filterCourses);
    }

    function filterCourses() {
        const activeTab = document.querySelector('.course-tab.active').dataset.tab;
        const searchTerm = searchInput ? searchInput.value.toLowerCase() : '';
        const selectedCategory = categoryFilter ? categoryFilter.value : 'all';
        const selectedLevel = levelFilter ? levelFilter.value : 'all';

        courseCards.forEach(card => {
            const cardType = card.dataset.type;
            const cardCategory = card.dataset.category;
            const cardLevel = card.dataset.level;
            const cardTitle = card.querySelector('h3').textContent.toLowerCase();
            const cardDescription = card.querySelector('p').textContent.toLowerCase();

            let shouldShow = true;

            // Filter by tab
            if (activeTab === 'gratis') {
                shouldShow = cardType === 'free' || card.querySelector('.text-green-600');
            } else if (activeTab === 'premium') {
                shouldShow = cardType === 'premium';
            } else if (activeTab === 'tutorial') {
                shouldShow = cardType === 'tutorial';
            } else if (activeTab === 'webinar') {
                shouldShow = cardType === 'webinar';
            }

            // Filter by search
            if (searchTerm && shouldShow) {
                shouldShow = cardTitle.includes(searchTerm) || cardDescription.includes(searchTerm);
            }

            // Filter by category
            if (selectedCategory !== 'all' && shouldShow) {
                shouldShow = cardCategory === selectedCategory;
            }

            // Filter by level
            if (selectedLevel !== 'all' && shouldShow) {
                shouldShow = cardLevel === selectedLevel;
            }

            card.style.display = shouldShow ? 'block' : 'none';
        });
    }

    // Mobile menu toggle
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const mobileMenu = document.querySelector('.mobile-menu');

    if (mobileMenuToggle && mobileMenu) {
        mobileMenuToggle.addEventListener('click', function() {
            mobileMenu.classList.toggle('hidden');
        });
    }
});
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\ngambiskuynew\resources\views/home.blade.php ENDPATH**/ ?>